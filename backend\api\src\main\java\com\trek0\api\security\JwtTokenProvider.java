package com.trek0.api.security;

import com.trek0.api.dto.UserDTO;

public class JwtTokenProvider {
    public String generateToken(UserDTO user) {
        return "jwt_token_" + user.getId();
    }
    
    public boolean validateToken(String token) {
        return token != null && token.startsWith("jwt_token_");
    }
    
    public Long getUserIdFromToken(String token) {
        if (validateToken(token)) {
            return Long.parseLong(token.substring(9));
        }
        return null;
    }
} 