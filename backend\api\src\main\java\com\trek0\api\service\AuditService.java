package com.trek0.api.service;

import com.trek0.api.dto.UserDTO;

public interface AuditService {
    void recordAuditEvent(String event, String details);
    void logSecurityException(String operation, String userId, String ipAddress, String reason);
    void logDataAccess(UserDTO user, String operation, String resource);
    void logApiCall(UserDTO user, String method, String path, String ip, int status, long duration);
} 