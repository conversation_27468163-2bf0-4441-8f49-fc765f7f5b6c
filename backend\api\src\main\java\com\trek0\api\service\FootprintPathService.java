package com.trek0.api.service;

import com.trek0.api.dto.FootprintPathDTO;
import com.trek0.api.dto.UserDTO;
import java.util.List;

public interface FootprintPathService {
    List<FootprintPathDTO> getFootprintPaths(UserDTO currentUser, String footprintId);
    FootprintPathDTO addPathToFootprint(UserDTO currentUser, String footprintId, Object request);
    FootprintPathDTO getPath(String pathId, UserDTO currentUser);
    FootprintPathDTO updatePath(UserDTO currentUser, String pathId, Object request);
    void deletePath(UserDTO currentUser, String pathId);
    List<FootprintPathDTO> searchFootprintPaths(UserD<PERSON> currentUser, String footprintId, String keyword);
} 