package com.trek0.api.service;

import com.trek0.api.dto.FootprintDTO;
import com.trek0.api.dto.FootprintPathDTO;
import com.trek0.api.dto.UserDTO;
import org.springframework.data.domain.Page;
import java.util.List;

public interface FootprintService {
    FootprintDTO createFootprint(UserDTO currentUser, Object request);
    FootprintDTO getFootprint(String footprintId, UserDTO currentUser);
    FootprintDTO updateFootprint(UserDTO currentUser, String footprintId, Object request);
    void deleteFootprint(UserDTO currentUser, String footprintId);
    List<FootprintPathDTO> getFootprintPaths(UserDTO currentUser, String footprintId);
    FootprintPathDTO addPathToFootprint(UserDTO currentUser, String footprintId, Object request);
    List<FootprintDTO> getPublicFootprints(Object pageable);
    List<FootprintDTO> getNearbyFootprints(Double lat, Double lng, Double radius);
    Page<FootprintDTO> getUserFootprints(User<PERSON><PERSON> currentUser, Object pageable);
    Page<FootprintDTO> searchUserFootprints(User<PERSON><PERSON> currentUser, String keyword, Object pageable);
} 