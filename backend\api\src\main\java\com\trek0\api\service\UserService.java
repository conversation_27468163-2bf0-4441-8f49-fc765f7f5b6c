package com.trek0.api.service;

import com.trek0.api.dto.UserDTO;
import java.util.List;
import java.util.Optional;

public interface UserService {
    Optional<UserDTO> findUserById(Long id);
    Optional<UserDTO> findUserByEmail(String email);
    Optional<UserDTO> findUserByUsername(String username);
    List<UserDTO> findAllUsers();
    UserDTO saveUser(UserDTO user);
    UserDTO registerUser(String email, String password, String uuid);
    UserDTO updateUserIpLocation(UserDTO user, String ipLocation);
    boolean changePassword(Long userId, String oldPassword, String newPassword);
    void deleteUser(Long userId);
} 