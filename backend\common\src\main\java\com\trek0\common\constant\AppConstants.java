package com.trek0.common.constant;

/**
 * 应用常量定义
 */
public final class AppConstants {
    
    private AppConstants() {
        // 私有构造函数，防止实例化
    }
    
    /**
     * API相关常量
     */
    public static final class Api {
        public static final String VERSION_V1 = "/api/v1";
        public static final String DEFAULT_PAGE_SIZE = "20";
        public static final String DEFAULT_PAGE_NUMBER = "0";
        public static final String USER_ID_HEADER = "X-User-Id";
    }
    
    /**
     * 足迹相关常量
     */
    public static final class Footprint {
        public static final double DEFAULT_NEARBY_RADIUS = 5.0; // km
        public static final int MAX_NAME_LENGTH = 100;
        public static final int MAX_DESCRIPTION_LENGTH = 1000;
        public static final int MAX_ADDRESS_LENGTH = 200;
        public static final int MAX_TAG_LENGTH = 50;
    }
    
    /**
     * 用户相关常量
     */
    public static final class User {
        public static final int MIN_PASSWORD_LENGTH = 6;
        public static final int MAX_USERNAME_LENGTH = 50;
        public static final int MAX_EMAIL_LENGTH = 100;
        public static final int MAX_SIGNATURE_LENGTH = 40;
    }
    
    /**
     * 缓存相关常量
     */
    public static final class Cache {
        public static final String FOOTPRINT_CACHE = "footprints";
        public static final String USER_CACHE = "users";
        public static final String PUBLIC_FOOTPRINTS_CACHE = "public_footprints";
        public static final int DEFAULT_TTL_MINUTES = 30;
    }
    
    /**
     * 消息相关常量
     */
    public static final class Messages {
        public static final String FOOTPRINT_CREATED = "足迹创建成功";
        public static final String FOOTPRINT_UPDATED = "足迹更新成功";
        public static final String FOOTPRINT_DELETED = "足迹删除成功";
        public static final String UNAUTHORIZED = "用户未登录";
        public static final String FORBIDDEN = "无权访问此资源";
        public static final String NOT_FOUND = "资源不存在";
    }
}