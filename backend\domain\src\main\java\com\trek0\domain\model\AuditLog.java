package com.trek0.domain.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

/**
 * 审计日志实体类
 * 记录系统中所有重要操作的审计信息
 */
@Entity
@Table(name = "audit_logs")
public class AuditLog {

    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "audit_id")
    private Long auditId;

    @NotBlank(message = "表名不能为空")
    @Column(name = "table_name", nullable = false, length = 100)
    private String tableName;

    @NotNull(message = "操作类型不能为空")
    @Enumerated(EnumType.STRING)
    @Column(name = "operation_type", nullable = false, length = 20)
    private OperationType operationType;

    @NotNull(message = "记录ID不能为空")
    @Column(name = "record_id", nullable = false, length = 50)
    private String recordId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id")
    @JsonBackReference
    private User user;

    @Column(name = "old_values", columnDefinition = "jsonb")
    private String oldValues;

    @Column(name = "new_values", columnDefinition = "jsonb")
    private String newValues;

    @Column(name = "ip_address")
    private String ipAddress;

    @Column(name = "user_agent", columnDefinition = "text")
    private String userAgent;

    @Column(name = "created_at", nullable = false, updatable = false)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    /**
     * 操作类型枚举
     */
    public enum OperationType {
        INSERT, UPDATE, DELETE
    }

    // 构造函数
    public AuditLog() {
    }

    public AuditLog(String tableName, OperationType operationType, String recordId) {
        this.tableName = tableName;
        this.operationType = operationType;
        this.recordId = recordId;
    }

    public AuditLog(String tableName, OperationType operationType, String recordId, User user) {
        this.tableName = tableName;
        this.operationType = operationType;
        this.recordId = recordId;
        this.user = user;
    }

    // JPA生命周期方法
    @PrePersist
    protected void onCreate() {
        if (createdAt == null) {
            createdAt = LocalDateTime.now();
        }
    }

    // 向后兼容性方法
    @JsonProperty("id")
    public Long getId() {
        return auditId;
    }

    public void setId(Long id) {
        this.auditId = id;
    }

    // Lombok注解移除，手动补全无参构造、getter/setter、equals、hashCode
    // public AuditLog() {} // 已有无参构造，无需重复

    public Long getAuditId() { return auditId; }
    public void setAuditId(Long auditId) { this.auditId = auditId; }
    public String getTableName() { return tableName; }
    public void setTableName(String tableName) { this.tableName = tableName; }
    public OperationType getOperationType() { return operationType; }
    public void setOperationType(OperationType operationType) { this.operationType = operationType; }
    public String getRecordId() { return recordId; }
    public void setRecordId(String recordId) { this.recordId = recordId; }
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    public String getOldValues() { return oldValues; }
    public void setOldValues(String oldValues) { this.oldValues = oldValues; }
    public String getNewValues() { return newValues; }
    public void setNewValues(String newValues) { this.newValues = newValues; }
    public String getIpAddress() { return ipAddress; }
    public void setIpAddress(String ipAddress) { this.ipAddress = ipAddress; }
    public String getUserAgent() { return userAgent; }
    public void setUserAgent(String userAgent) { this.userAgent = userAgent; }
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        AuditLog auditLog = (AuditLog) o;
        return java.util.Objects.equals(auditId, auditLog.auditId) &&
                java.util.Objects.equals(tableName, auditLog.tableName) &&
                operationType == auditLog.operationType &&
                java.util.Objects.equals(recordId, auditLog.recordId) &&
                java.util.Objects.equals(user, auditLog.user) &&
                java.util.Objects.equals(oldValues, auditLog.oldValues) &&
                java.util.Objects.equals(newValues, auditLog.newValues) &&
                java.util.Objects.equals(ipAddress, auditLog.ipAddress) &&
                java.util.Objects.equals(userAgent, auditLog.userAgent) &&
                java.util.Objects.equals(createdAt, auditLog.createdAt);
    }

    @Override
    public int hashCode() {
        return java.util.Objects.hash(auditId, tableName, operationType, recordId, user, oldValues, newValues, ipAddress, userAgent, createdAt);
    }

    // 业务方法
    public boolean isInsertOperation() {
        return OperationType.INSERT.equals(operationType);
    }

    public boolean isUpdateOperation() {
        return OperationType.UPDATE.equals(operationType);
    }

    public boolean isDeleteOperation() {
        return OperationType.DELETE.equals(operationType);
    }

    public boolean hasUser() {
        return user != null;
    }

    public boolean hasOldValues() {
        return oldValues != null && !oldValues.trim().isEmpty();
    }

    public boolean hasNewValues() {
        return newValues != null && !newValues.trim().isEmpty();
    }

    public String getOperationDescription() {
        StringBuilder desc = new StringBuilder();
        desc.append(operationType.name()).append(" operation on table '")
            .append(tableName).append("' for record ID ").append(recordId);
        
        if (user != null) {
            desc.append(" by user ").append(user.getUsername());
        }
        
        return desc.toString();
    }

    @Override
    public String toString() {
        return "AuditLog{" +
                "auditId=" + auditId +
                ", tableName='" + tableName + '\'' +
                ", operationType=" + operationType +
                ", recordId=" + recordId +
                ", userId=" + (user != null ? user.getUserId() : null) +
                ", createdAt=" + createdAt +
                '}';
    }
}