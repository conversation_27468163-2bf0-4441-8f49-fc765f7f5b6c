package com.trek0.domain.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonManagedReference;
import jakarta.persistence.*;
import jakarta.validation.constraints.Email;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Entity
@Table(name = "users")
public class User {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    @Column(name = "user_id")
    private Long userId;
    
    @Column(name = "user_uuid", unique = true, nullable = false, length = 36)
    private String userUuid;
    
    @Column(name = "username", unique = true, nullable = false, length = 50)
    @NotBlank(message = "Username is required")
    @Size(min = 3, max = 50, message = "Username must be between 3 and 50 characters")
    private String username;
    
    @Column(name = "email", unique = true, nullable = false, length = 100)
    @NotBlank(message = "Email is required")
    @Email(message = "Email should be valid")
    private String email;
    
    @Column(name = "password_hash", nullable = false, length = 255)
    @NotBlank(message = "Password is required")
    @JsonIgnore
    private String passwordHash;
    
    @Column(name = "user_signature", length = 40)
    @Size(max = 40, message = "Signature must be at most 40 characters")
    private String userSignature;
    
    @Column(name = "profile_image_url", length = 500)
    private String profileImageUrl;
    
    @Column(name = "last_ip_location", length = 255)
    private String lastIpLocation;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "account_status", nullable = false, length = 20)
    private AccountStatus accountStatus = AccountStatus.ACTIVE;
    
    @Column(name = "email_verified", nullable = false)
    private Boolean emailVerified = false;
    
    @Column(name = "created_at", nullable = false, updatable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at", nullable = false)
    private LocalDateTime updatedAt;
    
    @Column(name = "last_login_at")
    private LocalDateTime lastLoginAt;
    
    // 关注关系 - 我关注的人
    @OneToMany(mappedBy = "follower", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonManagedReference
    private List<UserFollow> following;
    
    // 关注关系 - 关注我的人
    @OneToMany(mappedBy = "following", cascade = CascadeType.ALL, orphanRemoval = true, fetch = FetchType.LAZY)
    @JsonManagedReference
    private List<UserFollow> followers;
    
    // 审计日志关联
    @OneToMany(mappedBy = "user", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @JsonManagedReference
    private List<AuditLog> auditLogs;
    
    // 账户状态枚举
    public enum AccountStatus {
        ACTIVE, SUSPENDED, DELETED
    }

    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }

    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }

    // Constructors
    public User() {}

    public User(String username, String email, String passwordHash) {
        this.username = username;
        this.email = email;
        this.passwordHash = passwordHash;
    }

    // Getters and Setters
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUserUuid() {
        return userUuid;
    }

    public void setUserUuid(String userUuid) {
        this.userUuid = userUuid;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() { return passwordHash; }

    public void setPasswordHash(String passwordHash) {
        this.passwordHash = passwordHash;
    }

    public String getUserSignature() {
        return userSignature;
    }

    public void setUserSignature(String userSignature) {
        this.userSignature = userSignature;
    }

    public String getProfileImageUrl() {
        return profileImageUrl;
    }

    public void setProfileImageUrl(String profileImageUrl) {
        this.profileImageUrl = profileImageUrl;
    }

    public String getLastIpLocation() {
        return lastIpLocation;
    }

    public void setLastIpLocation(String lastIpLocation) {
        this.lastIpLocation = lastIpLocation;
    }

    public AccountStatus getAccountStatus() {
        return accountStatus;
    }

    public void setAccountStatus(AccountStatus accountStatus) {
        this.accountStatus = accountStatus;
    }

    public Boolean getEmailVerified() {
        return emailVerified;
    }

    public void setEmailVerified(Boolean emailVerified) {
        this.emailVerified = emailVerified;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public LocalDateTime getLastLoginAt() {
        return lastLoginAt;
    }

    public void setLastLoginAt(LocalDateTime lastLoginAt) {
        this.lastLoginAt = lastLoginAt;
    }
    
    public List<UserFollow> getFollowing() {
        return following;
    }

    public void setFollowing(List<UserFollow> following) {
        this.following = following;
    }

    public List<UserFollow> getFollowers() {
        return followers;
    }

    public void setFollowers(List<UserFollow> followers) {
        this.followers = followers;
    }

    public List<AuditLog> getAuditLogs() {
        return auditLogs;
    }

    public void setAuditLogs(List<AuditLog> auditLogs) {
        this.auditLogs = auditLogs;
    }

    // 兼容service层
    public Long getId() { return userId; }
    public void setId(Long id) { this.userId = id; }
    public void setPassword(String password) { this.passwordHash = password; }
    public void setUuid(String uuid) { this.userUuid = uuid; }
    public void setIpLocation(String ipLocation) { this.lastIpLocation = ipLocation; }

    // 业务方法
    public boolean isActive() {
        return AccountStatus.ACTIVE.equals(this.accountStatus);
    }

    public boolean isSuspended() {
        return AccountStatus.SUSPENDED.equals(this.accountStatus);
    }

    public boolean isDeleted() {
        return AccountStatus.DELETED.equals(this.accountStatus);
    }

    public void activate() {
        this.accountStatus = AccountStatus.ACTIVE;
    }

    public void suspend() {
        this.accountStatus = AccountStatus.SUSPENDED;
    }

    public void delete() {
        this.accountStatus = AccountStatus.DELETED;
    }

    public void verifyEmail() {
        this.emailVerified = true;
    }

    public void updateLastLogin() {
        this.lastLoginAt = LocalDateTime.now();
    }

    @Override
    public String toString() {
        return "User{" +
                "userId=" + userId +
                ", userUuid='" + userUuid + '\'' +
                ", username='" + username + '\'' +
                ", email='" + email + '\'' +
                ", userSignature='" + userSignature + '\'' +
                ", profileImageUrl='" + profileImageUrl + '\'' +
                ", lastIpLocation='" + lastIpLocation + '\'' +
                ", accountStatus=" + accountStatus +
                ", emailVerified=" + emailVerified +
                ", createdAt=" + createdAt +
                ", updatedAt=" + updatedAt +
                ", lastLoginAt=" + lastLoginAt +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        User user = (User) o;
        return emailVerified == user.emailVerified &&
                accountStatus == user.accountStatus &&
                Objects.equals(userId, user.userId) &&
                Objects.equals(userUuid, user.userUuid) &&
                Objects.equals(username, user.username) &&
                Objects.equals(email, user.email) &&
                Objects.equals(passwordHash, user.passwordHash) &&
                Objects.equals(userSignature, user.userSignature) &&
                Objects.equals(profileImageUrl, user.profileImageUrl) &&
                Objects.equals(lastIpLocation, user.lastIpLocation) &&
                Objects.equals(createdAt, user.createdAt) &&
                Objects.equals(updatedAt, user.updatedAt) &&
                Objects.equals(lastLoginAt, user.lastLoginAt);
    }

    @Override
    public int hashCode() {
        return Objects.hash(userId, userUuid, username, email, passwordHash, userSignature, profileImageUrl, lastIpLocation, accountStatus, emailVerified, createdAt, updatedAt, lastLoginAt);
    }
}