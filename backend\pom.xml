<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.2.3</version>
        <relativePath/>
    </parent>

    <groupId>com.trek0</groupId>
    <artifactId>trek0-parent</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>trek0-parent</name>
    <description>TREK0 Multi-Module Parent Project</description>

    <modules>
        <module>api</module>
        <module>domain</module>
        <module>service</module>
        <module>infra</module>
        <module>web</module>
        <module>common</module>
        <module>config</module>
        <module>test</module>
    </modules>

    <properties>
        <java.version>24</java.version>
        <maven.compiler.source>24</maven.compiler.source>
        <maven.compiler.target>24</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        
        <!-- Spring Boot & Cloud -->
        <spring-boot.version>3.2.3</spring-boot.version>
        <spring-cloud.version>2023.0.0</spring-cloud.version>
        
        <!-- Database -->
        <postgresql.version>42.7.2</postgresql.version>
        <h2.version>2.2.224</h2.version>
        <hikaricp.version>5.1.0</hikaricp.version>
        <flyway.version>9.22.3</flyway.version>
        
        <!-- Security & JWT -->
        <jjwt.version>0.12.5</jjwt.version>
        
        <!-- Documentation -->
        <springdoc.version>2.3.0</springdoc.version>
        
        <!-- Monitoring & Metrics -->
        <micrometer.version>1.12.2</micrometer.version>
        <zipkin.version>2.24.3</zipkin.version>
        
        <!-- Rate Limiting & Resilience -->
        <bucket4j.version>7.6.0</bucket4j.version>
        <resilience4j.version>2.1.0</resilience4j.version>
        
        <!-- Utilities -->
        <lombok.version>1.18.30</lombok.version>
        <mapstruct.version>1.5.5.Final</mapstruct.version>
        
        <!-- Testing -->
        <testcontainers.version>1.19.3</testcontainers.version>
        <mockito.version>5.8.0</mockito.version>
        
        <!-- Build Plugins -->
        <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
        <maven-surefire-plugin.version>3.2.2</maven-surefire-plugin.version>
        <maven-failsafe-plugin.version>3.2.2</maven-failsafe-plugin.version>
        <jacoco-maven-plugin.version>0.8.8</jacoco-maven-plugin.version>
        <checkstyle-plugin.version>3.3.1</checkstyle-plugin.version>
        <spotless-plugin.version>2.41.1</spotless-plugin.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <!-- Spring Cloud BOM -->
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring-cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            
            <!-- Internal Modules -->
            <dependency>
                <groupId>com.trek0</groupId>
                <artifactId>trek0-api</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trek0</groupId>
                <artifactId>trek0-domain</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trek0</groupId>
                <artifactId>trek0-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trek0</groupId>
                <artifactId>trek0-infra</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trek0</groupId>
                <artifactId>trek0-web</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trek0</groupId>
                <artifactId>trek0-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.trek0</groupId>
                <artifactId>trek0-config</artifactId>
                <version>${project.version}</version>
            </dependency>
            
            <!-- Database -->
            <dependency>
                <groupId>org.postgresql</groupId>
                <artifactId>postgresql</artifactId>
                <version>${postgresql.version}</version>
            </dependency>
            <dependency>
                <groupId>com.h2database</groupId>
                <artifactId>h2</artifactId>
                <version>${h2.version}</version>
            </dependency>
            <dependency>
                <groupId>com.zaxxer</groupId>
                <artifactId>HikariCP</artifactId>
                <version>${hikaricp.version}</version>
            </dependency>
            
            <!-- JWT -->
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-api</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-impl</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            <dependency>
                <groupId>io.jsonwebtoken</groupId>
                <artifactId>jjwt-jackson</artifactId>
                <version>${jjwt.version}</version>
            </dependency>
            
            <!-- Documentation -->
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webmvc-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
            
            <!-- Rate Limiting -->
            <dependency>
                <groupId>com.github.vladimir-bukhtoyarov</groupId>
                <artifactId>bucket4j-core</artifactId>
                <version>${bucket4j.version}</version>
            </dependency>
            <dependency>
                <groupId>com.github.vladimir-bukhtoyarov</groupId>
                <artifactId>bucket4j-redis</artifactId>
                <version>${bucket4j.version}</version>
            </dependency>
            
            <!-- Resilience4j -->
            <dependency>
                <groupId>io.github.resilience4j</groupId>
                <artifactId>resilience4j-spring-boot3</artifactId>
                <version>${resilience4j.version}</version>
            </dependency>
            
            <!-- Utilities -->
            <dependency>
                <groupId>org.projectlombok</groupId>
                <artifactId>lombok</artifactId>
                <version>${lombok.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct-processor</artifactId>
                <version>${mapstruct.version}</version>
            </dependency>
            
            <!-- Testing -->
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>testcontainers-bom</artifactId>
                <version>${testcontainers.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.testcontainers</groupId>
                <artifactId>redis</artifactId>
                <version>${testcontainers.version}</version>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
            </dependency>
            
            <!-- Common Utilities -->
            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>2.11.0</version>
            </dependency>
            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>32.1.3-jre</version>
            </dependency>
            
            <!-- Servlet API -->
            <dependency>
                <groupId>javax.servlet</groupId>
                <artifactId>javax.servlet-api</artifactId>
                <version>4.0.1</version>
            </dependency>
            
            <!-- Flyway PostgreSQL -->
            <dependency>
                <groupId>org.flywaydb</groupId>
                <artifactId>flyway-database-postgresql</artifactId>
                <version>${flyway.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <build>
        <pluginManagement>
            <plugins>
                <!-- Compiler Plugin -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-compiler-plugin</artifactId>
                    <version>${maven-compiler-plugin.version}</version>
                    <configuration>
                        <source>${java.version}</source>
                        <target>${java.version}</target>
                        <encoding>${project.build.sourceEncoding}</encoding>
                        <annotationProcessorPaths>
                            <path>
                                <groupId>org.mapstruct</groupId>
                                <artifactId>mapstruct-processor</artifactId>
                                <version>${mapstruct.version}</version>
                            </path>
                        </annotationProcessorPaths>
                    </configuration>
                </plugin>
                
                <!-- Spring Boot Plugin -->
                <plugin>
                    <groupId>org.springframework.boot</groupId>
                    <artifactId>spring-boot-maven-plugin</artifactId>
                    <version>${spring-boot.version}</version>
                    <configuration>
                        <excludes>
                            <exclude>
                                <groupId>org.projectlombok</groupId>
                                <artifactId>lombok</artifactId>
                            </exclude>
                        </excludes>
                    </configuration>
                </plugin>
                
                <!-- Flyway Plugin -->
                <plugin>
                    <groupId>org.flywaydb</groupId>
                    <artifactId>flyway-maven-plugin</artifactId>
                    <version>${flyway.version}</version>
                </plugin>
                
                <!-- Test Plugins -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-surefire-plugin</artifactId>
                    <version>${maven-surefire-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-failsafe-plugin</artifactId>
                    <version>${maven-failsafe-plugin.version}</version>
                </plugin>
                
                <!-- Code Coverage -->
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco-maven-plugin.version}</version>
                </plugin>
                
                <!-- Code Style -->
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-checkstyle-plugin</artifactId>
                    <version>${checkstyle-plugin.version}</version>
                </plugin>
                <plugin>
                    <groupId>com.diffplug.spotless</groupId>
                    <artifactId>spotless-maven-plugin</artifactId>
                    <version>${spotless-plugin.version}</version>
                </plugin>
            </plugins>
        </pluginManagement>
        
        <plugins>
            <!-- Enable compiler plugin for all modules -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
            </plugin>
            
            <!-- Enable test coverage for all modules -->
            <plugin>
                <groupId>org.jacoco</groupId>
                <artifactId>jacoco-maven-plugin</artifactId>
                <executions>
                    <execution>
                        <goals>
                            <goal>prepare-agent</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>report</id>
                        <phase>test</phase>
                        <goals>
                            <goal>report</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <spring.profiles.active>dev</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <spring.profiles.active>test</spring.profiles.active>
            </properties>
        </profile>
        <profile>
            <id>prod</id>
            <properties>
                <spring.profiles.active>prod</spring.profiles.active>
            </properties>
        </profile>
    </profiles>
</project>
