package com.trek0.service;

import com.trek0.domain.model.User;

import java.util.List;
import java.util.Optional;

public interface UserService {
    List<User> findAllUsers();
    Optional<User> findUserById(Long id);
    Optional<User> findUserByUsername(String username);
    Optional<User> findUserByEmail(String email);
    User saveUser(User user);
    void deleteUser(Long id);
    User registerUser(String email, String password, String uuid);
    boolean resetPassword(String email, String code, String newPassword);
    void updateUserIpLocation(User user, String ip);
    boolean changePassword(Long userId, String oldPassword, String newPassword);
}