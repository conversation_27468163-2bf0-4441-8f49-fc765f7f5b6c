package com.trek0.service.impl;

import com.trek0.domain.model.User;
import com.trek0.domain.repository.UserRepository;
import com.trek0.service.UserService;
import io.github.resilience4j.circuitbreaker.annotation.CircuitBreaker;
import io.github.resilience4j.retry.annotation.Retry;
import io.github.resilience4j.timelimiter.annotation.TimeLimiter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class UserServiceImpl implements UserService {
    private static final Logger logger = LoggerFactory.getLogger(UserServiceImpl.class);
    
    private final UserRepository userRepository;
    private final PasswordEncoder passwordEncoder;
    
    @Autowired
    public UserServiceImpl(UserRepository userRepository, 
                          PasswordEncoder passwordEncoder) {
        this.userRepository = userRepository;
        this.passwordEncoder = passwordEncoder;
    }
    
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "findAllUsersFallback")
    @Retry(name = "database")
    public List<User> findAllUsers() {
        try {
            return userRepository.findAll();
        } catch (Exception e) {
            logger.error("查找所有用户失败", e);
            throw e;
        }
    }
    
    public List<User> findAllUsersFallback(Exception ex) {
        logger.warn("查找所有用户降级处理: {}", ex.getMessage());
        return List.of(); // 返回空列表作为降级处理
    }
    
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "deleteUserFallback")
    @Retry(name = "database")
    public void deleteUser(Long id) {
        try {
            userRepository.deleteById(id);
        } catch (Exception e) {
            logger.error("删除用户失败: userId={}", id, e);
            throw e;
        }
    }
    
    public void deleteUserFallback(Long id, Exception ex) {
        logger.warn("删除用户降级处理: userId={}, error={}", id, ex.getMessage());
        throw new RuntimeException("用户删除服务暂时不可用，请稍后重试");
    }
    
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "findUserByIdFallback")
    @Retry(name = "database")
    public Optional<User> findUserById(Long id) {
        try {
            return userRepository.findById(id);
        } catch (Exception e) {
            logger.error("根据ID查找用户失败: userId={}", id, e);
            throw e;
        }
    }
    
    public Optional<User> findUserByIdFallback(Long id, Exception ex) {
        logger.warn("根据ID查找用户降级处理: userId={}, error={}", id, ex.getMessage());
        return Optional.empty();
    }
    
    @Override
    public Optional<User> findUserByUsername(String username) {
        try {
            return userRepository.findByUsername(username);
        } catch (Exception e) {
            logger.error("根据用户名查找用户失败: username={}", username, e);
            throw e;
        }
    }
    
    public Optional<User> findUserByUsernameFallback(String username, Exception ex) {
        logger.warn("根据用户名查找用户降级处理: username={}, error={}", username, ex.getMessage());
        return Optional.empty();
    }
    
    @Override
    public Optional<User> findUserByEmail(String email) {
        try {
            return userRepository.findByEmail(email);
        } catch (Exception e) {
            logger.error("根据邮箱查找用户失败: email={}", email, e);
            throw e;
        }
    }
    
    public Optional<User> findUserByEmailFallback(String email, Exception ex) {
        logger.warn("根据邮箱查找用户降级处理: email={}, error={}", email, ex.getMessage());
        return Optional.empty();
    }
    
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "saveUserFallback")
    @Retry(name = "database")
    public User saveUser(User user) {
        try {
            return userRepository.save(user);
        } catch (Exception e) {
            logger.error("保存用户失败: userId={}", user.getId(), e);
            throw e;
        }
    }
    
    public User saveUserFallback(User user, Exception ex) {
        logger.warn("保存用户降级处理: userId={}, error={}", user.getId(), ex.getMessage());
        throw new RuntimeException("用户保存服务暂时不可用，请稍后重试");
    }
    
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "registerUserFallback")
    @Retry(name = "database")
    public User registerUser(String email, String password, String uuid) {
        try {
            User user = new User();
            user.setEmail(email);
            user.setPassword(passwordEncoder.encode(password));
            user.setUuid(uuid);
            user.setUsername(uuid); // 设置用户名为uuid
            return userRepository.save(user);
        } catch (Exception e) {
            logger.error("注册用户失败: email={}", email, e);
            throw e;
        }
    }
    
    public User registerUserFallback(String email, String password, String uuid, Exception ex) {
        logger.warn("注册用户降级处理: email={}, error={}", email, ex.getMessage());
        throw new RuntimeException("用户注册服务暂时不可用，请稍后重试");
    }
    
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "resetPasswordFallback")
    @Retry(name = "database")
    public boolean resetPassword(String email, String code, String newPassword) {
        try {
            Optional<User> userOpt = userRepository.findByEmail(email);
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                user.setPassword(newPassword);
                userRepository.save(user);
                return true;
            }
            return false;
        } catch (Exception e) {
            logger.error("重置密码失败: email={}", email, e);
            throw e;
        }
    }
    
    public boolean resetPasswordFallback(String email, String code, String newPassword, Exception ex) {
        logger.warn("重置密码降级处理: email={}, error={}", email, ex.getMessage());
        return false;
    }
    
    @Override
    @CircuitBreaker(name = "external-api", fallbackMethod = "updateUserIpLocationFallback")
    @Retry(name = "external-api")
    public void updateUserIpLocation(User user, String ip) {
        try {
            // String location = ipLocationUtil.getRegion(ip); // Removed as per edit hint
            // if (location != null && !location.equals(user.getIpLocation())) { // Removed as per edit hint
                user.setIpLocation(null); // Placeholder for actual IP location update
                userRepository.save(user);
            // }
        } catch (Exception e) {
            logger.error("更新用户IP位置失败: userId={}, ip={}", user.getId(), ip, e);
            throw e;
        }
    }
    
    public void updateUserIpLocationFallback(User user, String ip, Exception ex) {
        logger.warn("更新用户IP位置降级处理: userId={}, ip={}, error={}", user.getId(), ip, ex.getMessage());
        // 降级处理：不更新位置信息，但不抛出异常
    }
    
    @Override
    @CircuitBreaker(name = "database", fallbackMethod = "changePasswordFallback")
    @Retry(name = "database")
    public boolean changePassword(Long userId, String oldPassword, String newPassword) {
        try {
            Optional<User> userOpt = userRepository.findById(userId);
            if (userOpt.isEmpty()) return false;
            User user = userOpt.get();
            if (!passwordEncoder.matches(oldPassword, user.getPassword())) return false;
            user.setPassword(passwordEncoder.encode(newPassword));
            userRepository.save(user);
            return true;
        } catch (Exception e) {
            logger.error("修改密码失败: userId={}", userId, e);
            throw e;
        }
    }
    
    public boolean changePasswordFallback(Long userId, String oldPassword, String newPassword, Exception ex) {
        logger.warn("修改密码降级处理: userId={}, error={}", userId, ex.getMessage());
        return false;
    }
}