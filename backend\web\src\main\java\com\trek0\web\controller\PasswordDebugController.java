package com.trek0.web.controller;

import com.trek0.api.dto.UserDTO;
import com.trek0.api.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

@RestController
@RequestMapping("/api/debug")
public class PasswordDebugController {
    
    private final UserService userService;
    private final PasswordEncoder passwordEncoder;
    
    @Autowired
    public PasswordDebugController(UserService userService, PasswordEncoder passwordEncoder) {
        this.userService = userService;
        this.passwordEncoder = passwordEncoder;
    }
    
    @GetMapping("/check-password/{email}")
    public ResponseEntity<?> checkPassword(@PathVariable String email) {
        Optional<UserDTO> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        UserDTO user = userOpt.get();
        Map<String, Object> response = new HashMap<>();
        response.put("email", user.getEmail());
        response.put("passwordHash", user.getPassword());
        response.put("passwordLength", user.getPassword().length());
        response.put("isBCryptHash", user.getPassword().startsWith("$2"));
        
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/fix-password")
    public ResponseEntity<?> fixPassword(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String plainPassword = request.get("password");
        
        Optional<UserDTO> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        UserDTO user = userOpt.get();
        String oldPassword = user.getPassword();
        
        // 检查当前密码是否已经是BCrypt格式
        if (user.getPassword().startsWith("$2")) {
            // 已经是BCrypt格式，检查是否匹配
            boolean matches = passwordEncoder.matches(plainPassword, user.getPassword());
            Map<String, Object> response = new HashMap<>();
            response.put("message", "密码已经是BCrypt格式");
            response.put("matches", matches);
            return ResponseEntity.ok(response);
        }
        
        // 如果密码是明文或其他格式，重新编码为BCrypt
        String encodedPassword = passwordEncoder.encode(plainPassword);
        user.setPassword(encodedPassword);
        userService.saveUser(user);
        
        Map<String, Object> response = new HashMap<>();
        response.put("message", "密码已更新为BCrypt格式");
        response.put("oldPassword", oldPassword);
        response.put("newPassword", encodedPassword);
        
        return ResponseEntity.ok(response);
    }
    
    @PostMapping("/test-login")
    public ResponseEntity<?> testLogin(@RequestBody Map<String, String> request) {
        String email = request.get("email");
        String password = request.get("password");
        
        Optional<UserDTO> userOpt = userService.findUserByEmail(email);
        if (userOpt.isEmpty()) {
            return ResponseEntity.notFound().build();
        }
        
        UserDTO user = userOpt.get();
        boolean matches = passwordEncoder.matches(password, user.getPassword());
        
        Map<String, Object> response = new HashMap<>();
        response.put("email", email);
        response.put("passwordMatches", matches);
        response.put("storedPasswordHash", user.getPassword());
        
        return ResponseEntity.ok(response);
    }
}