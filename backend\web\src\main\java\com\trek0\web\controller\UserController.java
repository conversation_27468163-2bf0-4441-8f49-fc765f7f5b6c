package com.trek0.web.controller;

import com.trek0.api.dto.UserDTO;
import com.trek0.api.service.UserService;
import com.trek0.common.metrics.BusinessMetrics;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/v1/users")
@Tag(name = "用户管理", description = "用户相关API")
public class UserController {
    
    private static final Logger logger = LoggerFactory.getLogger(UserController.class);
    
    private final UserService userService;
    private final BusinessMetrics businessMetrics;
    
    @Value("${hcaptcha.sitekey}")
    private String hcaptchaSiteKey;
    @Value("${amap.key}")
    private String amapKey;
    @Value("${baidu.key}")
    private String baiduKey;

    @Autowired
    public UserController(UserService userService, BusinessMetrics businessMetrics) {
        this.userService = userService;
        this.businessMetrics = businessMetrics;
    }

    @GetMapping
    @Operation(summary = "获取所有用户", description = "获取系统中所有用户列表")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功")
    })
    public List<UserDTO> getAllUsers() {
        try {
            return userService.findAllUsers();
        } catch (Exception e) {
            logger.error("获取所有用户失败", e);
            businessMetrics.incrementApiError("get_all_users_failed");
            throw e;
        }
    }

    @DeleteMapping("/delete/{id}")
    @Operation(summary = "删除用户", description = "根据用户ID删除用户")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "删除成功"),
        @ApiResponse(responseCode = "404", description = "用户不存在")
    })
    public ResponseEntity<Void> deleteUser(
            @Parameter(description = "用户ID", required = true)
            @PathVariable Long id) {
        try {
            return userService.findUserById(id)
                    .map(user -> {
                        userService.deleteUser(id);
                        return ResponseEntity.ok().<Void>build();
                    })
                    .orElse(ResponseEntity.notFound().build());
        } catch (Exception e) {
            logger.error("删除用户失败: userId={}", id, e);
            businessMetrics.incrementApiError("delete_user_failed");
            throw e;
        }
    }

    @GetMapping(value = "/check-email", produces = MediaType.APPLICATION_JSON_VALUE)
    @Operation(summary = "检查邮箱", description = "检查邮箱是否已注册")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "检查成功"),
        @ApiResponse(responseCode = "400", description = "邮箱格式错误")
    })
    public ResponseEntity<?> checkEmailRegistered(
            @Parameter(description = "邮箱地址", required = true)
            @RequestParam String email) {
        try {
            if (!StringUtils.hasText(email) || !email.matches("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+$")) {
                businessMetrics.incrementApiError("invalid_email_format");
                return ResponseEntity.badRequest().body(java.util.Collections.singletonMap("error", "邮箱参数无效"));
            }
            boolean exists = userService.findUserByEmail(email).isPresent();
            return ResponseEntity.ok(java.util.Collections.singletonMap("exists", exists));
        } catch (Exception e) {
            logger.error("检查邮箱失败: email={}", email, e);
            businessMetrics.incrementApiError("check_email_failed");
            throw e;
        }
    }

    @GetMapping("/public-config")
    @Operation(summary = "获取公共配置", description = "获取前端需要的公共配置信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功")
    })
    public Map<String, Object> getPublicConfig() {
        try {
            Map<String, Object> map = new HashMap<>();
            map.put("hcaptchaSiteKey", hcaptchaSiteKey);
            map.put("amapKey", amapKey);
            map.put("baiduKey", baiduKey);
            return map;
        } catch (Exception e) {
            logger.error("获取公共配置失败", e);
            businessMetrics.incrementApiError("get_public_config_failed");
            throw e;
        }
    }

    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "获取成功"),
        @ApiResponse(responseCode = "401", description = "未登录")
    })
    public ResponseEntity<?> getCurrentUser() {
        try {
            // 从Spring Security上下文获取当前用户
            org.springframework.security.core.Authentication authentication = 
                org.springframework.security.core.context.SecurityContextHolder.getContext().getAuthentication();
            
            if (authentication == null || !authentication.isAuthenticated() || 
                "anonymousUser".equals(authentication.getName())) {
                return ResponseEntity.status(401).body(java.util.Collections.singletonMap("error", "未登录"));
            }
            
            String username = authentication.getName();
            java.util.Optional<UserDTO> userOpt = userService.findUserByEmail(username);
            if (userOpt.isEmpty()) {
                userOpt = userService.findUserByUsername(username);
            }
            
            if (userOpt.isPresent()) {
                UserDTO user = userOpt.get();
                // 不返回密码
                user.setPassword(null);
                return ResponseEntity.ok(user);
            }
            
            return ResponseEntity.status(401).body(java.util.Collections.singletonMap("error", "用户不存在"));
        } catch (Exception e) {
            logger.error("获取当前用户信息失败", e);
            businessMetrics.incrementApiError("get_current_user_failed");
            throw e;
        }
    }
}